2025-06-17 23:14:33,556 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-17 23:14:33,556 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-17 23:20:01,069 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-17 23:20:01,069 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-17 23:20:36,895 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-17 23:20:36,896 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-17 23:42:39,981 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-17 23:42:39,981 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-17 23:43:05,281 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-17 23:43:05,281 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-17 23:52:37,920 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-17 23:52:37,921 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 00:16:32,519 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 00:16:32,519 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 01:51:43,710 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 01:51:43,711 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:01:59,754 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:01:59,754 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:02:45,747 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:02:45,747 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:05:05,815 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:05:05,815 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:05:32,759 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:05:32,759 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:06:46,431 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:06:46,432 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:15:19,929 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:15:19,930 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:30:35,188 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:30:35,188 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:33:24,173 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:33:24,173 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:45:24,289 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:45:24,289 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:53:18,852 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:53:18,853 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 02:55:09,199 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 02:55:09,199 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 03:02:52,540 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 03:02:52,541 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 03:07:56,270 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 03:07:56,270 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
2025-06-18 03:12:27,320 - APIClient - DEBUG - debug:111 - 会话配置: 会话超时设置为 30 秒
2025-06-18 03:12:27,320 - APIClient - INFO - info:116 - API客户端: API客户端初始化完成
