# -*- coding: utf-8 -*-
"""
简化版现代化UI
解决兼容性问题的简洁界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel,
    QPushButton, QStackedWidget, QTextEdit, QTableWidget, QHeaderView,
    QTableWidgetItem, QProgressBar, QSpinBox, QCheckBox, QFileDialog,
    QMessageBox, QLineEdit, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont
import re
from typing import List, Dict, Any
from datetime import datetime


class SimpleSidebar(QFrame):
    """简化版侧边栏"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_page = "dashboard"
        self.buttons = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化侧边栏UI"""
        self.setFixedWidth(250)
        self.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border-right: 1px solid #e5e7eb;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo区域
        logo_area = self.create_logo_area()
        layout.addWidget(logo_area)
        
        # 导航菜单
        nav_area = self.create_navigation_area()
        layout.addWidget(nav_area)
        
        layout.addStretch()
    
    def create_logo_area(self) -> QWidget:
        """创建Logo区域"""
        container = QFrame()
        container.setFixedHeight(70)
        container.setStyleSheet("""
            QFrame {
                background: #2563eb;
                border: none;
            }
        """)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Logo和标题
        title_label = QLabel("🚀 团队管理")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        
        layout.addWidget(title_label)
        layout.addStretch()
        
        return container
    
    def create_navigation_area(self) -> QWidget:
        """创建导航区域"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 15, 0, 0)
        layout.setSpacing(2)
        
        # 导航项目
        nav_items = [
            ("dashboard", "📊 仪表盘"),
            ("invite", "✉️ 邀请成员"),
            ("manage", "👥 团队管理"),
            ("batch", "⚡ 批量操作"),
            ("data", "📈 数据分析"),
            ("settings", "⚙️ 设置")
        ]
        
        for page_id, title in nav_items:
            btn = self.create_nav_button(page_id, title)
            self.buttons[page_id] = btn
            layout.addWidget(btn)
        
        # 设置默认选中
        self.set_active_button("dashboard")
        
        return container
    
    def create_nav_button(self, page_id: str, title: str) -> QPushButton:
        """创建导航按钮"""
        btn = QPushButton(title)
        btn.setFixedHeight(45)
        btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 12px 20px;
                border: none;
                background: transparent;
                color: #111827;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: #f9fafb;
            }
            QPushButton[active="true"] {
                background: #dbeafe;
                color: #2563eb;
                border-right: 3px solid #2563eb;
                font-weight: bold;
            }
        """)
        
        btn.clicked.connect(lambda: self.switch_page(page_id))
        return btn
    
    def switch_page(self, page_id: str):
        """切换页面"""
        if page_id != self.current_page:
            self.set_active_button(page_id)
            self.current_page = page_id
            self.page_changed.emit(page_id)
    
    def set_active_button(self, page_id: str):
        """设置活动按钮"""
        for btn_id, btn in self.buttons.items():
            btn.setProperty("active", btn_id == page_id)
            btn.style().unpolish(btn)
            btn.style().polish(btn)


class SimpleContentArea(QStackedWidget):
    """简化版内容区域"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.pages = {}
        self.api_client = None
        self.team_data = None
        self.worker_thread = None
        self.init_pages()
    
    def init_pages(self):
        """初始化页面"""
        # 仪表盘页面
        dashboard_page = self.create_dashboard_page()
        self.addWidget(dashboard_page)
        self.pages["dashboard"] = dashboard_page

        # 邀请成员页面
        invite_page = self.create_invite_page()
        self.addWidget(invite_page)
        self.pages["invite"] = invite_page

        # 团队管理页面
        manage_page = self.create_manage_page()
        self.addWidget(manage_page)
        self.pages["manage"] = manage_page

        # 批量操作页面
        batch_page = self.create_batch_page()
        self.addWidget(batch_page)
        self.pages["batch"] = batch_page

        # 数据分析页面
        data_page = self.create_data_page()
        self.addWidget(data_page)
        self.pages["data"] = data_page

        # 设置页面
        settings_page = self.create_settings_page()
        self.addWidget(settings_page)
        self.pages["settings"] = settings_page
    
    def create_dashboard_page(self) -> QWidget:
        """创建仪表盘页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("📊 仪表盘")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 统计卡片区域
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)

        # 创建统计卡片
        self.total_members_card = self.create_stat_card("👥", "团队成员", "0")
        self.pending_invites_card = self.create_stat_card("📧", "待处理邀请", "0")
        self.today_invites_card = self.create_stat_card("✅", "今日邀请", "0")
        self.success_rate_card = self.create_stat_card("📊", "成功率", "0%")

        stats_layout.addWidget(self.total_members_card)
        stats_layout.addWidget(self.pending_invites_card)
        stats_layout.addWidget(self.today_invites_card)
        stats_layout.addWidget(self.success_rate_card)

        layout.addLayout(stats_layout)

        # 快速操作区域
        quick_actions_card = self.create_quick_actions_card()
        layout.addWidget(quick_actions_card)

        # 最近活动区域
        recent_activity_card = self.create_recent_activity_card()
        layout.addWidget(recent_activity_card)

        layout.addStretch()
        return page

    def create_stat_card(self, icon: str, title: str, value: str) -> QFrame:
        """创建统计卡片"""
        card = QFrame()
        card.setFixedHeight(80)
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                border-left: 4px solid #2563eb;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            font-size: 24px;
            color: #2563eb;
            min-width: 40px;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 内容
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)

        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
        """)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 12px;
            color: #6b7280;
        """)

        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)

        layout.addWidget(icon_label)
        layout.addLayout(content_layout)
        layout.addStretch()

        # 保存值标签以便更新
        card.value_label = value_label

        return card

    def create_quick_actions_card(self) -> QFrame:
        """创建快速操作卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("⚡ 快速操作")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 快速操作按钮
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.setStyleSheet(self.get_button_style("primary"))
        refresh_btn.clicked.connect(self.refresh_team_data)

        invite_btn = QPushButton("📧 邀请成员")
        invite_btn.setStyleSheet(self.get_button_style("success"))
        invite_btn.clicked.connect(lambda: self.switch_to_page("invite"))

        manage_btn = QPushButton("👥 团队管理")
        manage_btn.setStyleSheet(self.get_button_style("info"))
        manage_btn.clicked.connect(lambda: self.switch_to_page("manage"))

        settings_btn = QPushButton("⚙️ 设置")
        settings_btn.setStyleSheet(self.get_button_style("secondary"))
        settings_btn.clicked.connect(lambda: self.switch_to_page("settings"))

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(invite_btn)
        button_layout.addWidget(manage_btn)
        button_layout.addWidget(settings_btn)

        layout.addLayout(button_layout)

        return card

    def create_recent_activity_card(self) -> QFrame:
        """创建最近活动卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📋 最近活动")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # 活动列表
        self.activity_list = QWidget()
        activity_layout = QVBoxLayout(self.activity_list)
        activity_layout.setSpacing(8)

        # 默认活动
        self.add_activity("🚀", "应用启动", "刚刚")

        layout.addWidget(self.activity_list)

        return card

    def add_activity(self, icon: str, text: str, time: str):
        """添加活动记录"""
        activity_item = QFrame()
        activity_item.setStyleSheet("""
            QFrame {
                background: #f9fafb;
                border-radius: 6px;
                padding: 8px;
            }
        """)

        layout = QHBoxLayout(activity_item)
        layout.setContentsMargins(12, 8, 12, 8)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 14px;")

        # 文本
        text_label = QLabel(text)
        text_label.setStyleSheet("color: #374151; font-weight: 500;")

        # 时间
        time_label = QLabel(time)
        time_label.setStyleSheet("color: #6b7280; font-size: 12px;")

        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()
        layout.addWidget(time_label)

        # 添加到活动列表
        self.activity_list.layout().addWidget(activity_item)

    def create_simple_page(self, title: str, description: str) -> QWidget:
        """创建简单页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # 页面描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 20px;
        """)
        layout.addWidget(desc_label)
        
        # 内容卡片
        content_card = self.create_content_card()
        layout.addWidget(content_card)
        
        layout.addStretch()
        return page
    
    def create_content_card(self) -> QWidget:
        """创建内容卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 卡片内容
        content_label = QLabel("功能正在开发中...")
        content_label.setStyleSheet("""
            font-size: 16px;
            color: #374151;
            text-align: center;
            padding: 40px;
        """)
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addWidget(content_label)
        
        return card

    def create_invite_page(self) -> QWidget:
        """创建邀请成员页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("✉️ 邀请成员")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 邀请表单卡片
        invite_form_card = self.create_invite_form_card()
        layout.addWidget(invite_form_card)

        # 邀请历史卡片
        invite_history_card = self.create_invite_history_card()
        layout.addWidget(invite_history_card)

        layout.addStretch()
        return page

    def create_invite_form_card(self) -> QFrame:
        """创建邀请表单卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("📧 发送邀请")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 邮箱输入区域
        email_label = QLabel("邮箱地址（每行一个）:")
        email_label.setStyleSheet("color: #374151; font-weight: 500;")
        layout.addWidget(email_label)

        self.email_input = QTextEdit()
        self.email_input.setFixedHeight(120)
        self.email_input.setPlaceholderText("请输入邮箱地址，每行一个\n例如：\<EMAIL>\<EMAIL>")
        self.email_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                background: #ffffff;
            }
            QTextEdit:focus {
                border-color: #2563eb;
            }
        """)
        layout.addWidget(self.email_input)

        # 进度条
        self.invite_progress = QProgressBar()
        self.invite_progress.setVisible(False)
        self.invite_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e5e7eb;
                border-radius: 4px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #2563eb;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.invite_progress)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 发送邀请按钮
        self.send_invite_btn = QPushButton("📧 发送邀请")
        self.send_invite_btn.setStyleSheet(self.get_button_style("primary"))
        self.send_invite_btn.clicked.connect(self.send_invitations)

        # 清空按钮
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.setStyleSheet(self.get_button_style("secondary"))
        clear_btn.clicked.connect(self.clear_email_input)

        # 导入文件按钮
        import_btn = QPushButton("📁 导入文件")
        import_btn.setStyleSheet(self.get_button_style("info"))
        import_btn.clicked.connect(self.import_email_file)

        button_layout.addWidget(self.send_invite_btn)
        button_layout.addWidget(clear_btn)
        button_layout.addWidget(import_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        return card

    def create_invite_history_card(self) -> QFrame:
        """创建邀请历史卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📋 邀请历史")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # 邀请历史表格
        self.invite_history_table = QTableWidget()
        self.invite_history_table.setColumnCount(4)
        self.invite_history_table.setHorizontalHeaderLabels(["邮箱", "状态", "邀请时间", "操作"])

        # 设置表格样式
        self.invite_history_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                background: #ffffff;
                gridline-color: #f3f4f6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f3f4f6;
            }
            QHeaderView::section {
                background: #f9fafb;
                padding: 10px;
                border: none;
                font-weight: bold;
                color: #374151;
            }
        """)

        # 设置表格属性
        header = self.invite_history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        self.invite_history_table.setAlternatingRowColors(True)
        self.invite_history_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        layout.addWidget(self.invite_history_table)

        return card

    def switch_to_page(self, page_id: str):
        """切换到指定页面"""
        if page_id in self.pages:
            page_widget = self.pages[page_id]
            self.setCurrentWidget(page_widget)

    def set_api_client(self, api_client):
        """设置API客户端"""
        self.api_client = api_client

    def get_button_style(self, button_type: str) -> str:
        """获取按钮样式"""
        styles = {
            "primary": """
                QPushButton {
                    background: #2563eb;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #1d4ed8;
                }
                QPushButton:pressed {
                    background: #1e40af;
                }
                QPushButton:disabled {
                    background: #9ca3af;
                }
            """,
            "success": """
                QPushButton {
                    background: #10b981;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #059669;
                }
                QPushButton:pressed {
                    background: #047857;
                }
            """,
            "info": """
                QPushButton {
                    background: #06b6d4;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #0891b2;
                }
                QPushButton:pressed {
                    background: #0e7490;
                }
            """,
            "secondary": """
                QPushButton {
                    background: #6b7280;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #4b5563;
                }
                QPushButton:pressed {
                    background: #374151;
                }
            """,
            "danger": """
                QPushButton {
                    background: #ef4444;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #dc2626;
                }
                QPushButton:pressed {
                    background: #b91c1c;
                }
            """
        }
        return styles.get(button_type, styles["primary"])

    def validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def send_invitations(self):
        """发送邀请"""
        if not self.api_client:
            self.show_message("错误", "API客户端未初始化", "error")
            return

        text = self.email_input.toPlainText().strip()
        if not text:
            self.show_message("提示", "请输入邮箱地址", "warning")
            return

        # 解析邮箱列表
        emails = [email.strip() for email in text.split('\n') if email.strip()]
        valid_emails = [email for email in emails if self.validate_email(email)]
        invalid_emails = [email for email in emails if not self.validate_email(email)]

        if invalid_emails:
            self.show_message("警告", f"发现 {len(invalid_emails)} 个无效邮箱，将被忽略", "warning")

        if not valid_emails:
            self.show_message("错误", "没有找到有效的邮箱地址", "error")
            return

        # 显示进度条
        self.invite_progress.setVisible(True)
        self.invite_progress.setValue(0)
        self.send_invite_btn.setEnabled(False)
        self.send_invite_btn.setText("发送中...")

        # 启动工作线程
        self.start_invite_worker(valid_emails)

        # 添加活动记录
        self.add_activity("📧", f"开始邀请 {len(valid_emails)} 个成员", "刚刚")

    def clear_email_input(self):
        """清空邮箱输入"""
        self.email_input.clear()

    def import_email_file(self):
        """导入邮箱文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择邮箱文件", "",
            "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 解析文件内容
                emails = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and '@' in line:
                        # 简单的邮箱提取
                        if ',' in line:
                            emails.extend([email.strip() for email in line.split(',')])
                        else:
                            emails.append(line)

                # 添加到输入框
                current_text = self.email_input.toPlainText()
                if current_text:
                    current_text += '\n'
                current_text += '\n'.join(emails)
                self.email_input.setPlainText(current_text)

                self.show_message("成功", f"成功导入 {len(emails)} 个邮箱地址", "success")

            except Exception as e:
                self.show_message("错误", f"文件导入失败: {str(e)}", "error")

    def refresh_team_data(self):
        """刷新团队数据"""
        if not self.api_client:
            self.show_message("错误", "API客户端未初始化", "error")
            return

        # 启动数据刷新工作线程
        self.start_refresh_worker()
        self.add_activity("🔄", "刷新团队数据", "刚刚")

    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """显示消息"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        if msg_type == "error":
            msg_box.setIcon(QMessageBox.Icon.Critical)
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Icon.Warning)
        elif msg_type == "success":
            msg_box.setIcon(QMessageBox.Icon.Information)
        else:
            msg_box.setIcon(QMessageBox.Icon.Information)

        msg_box.exec()

    def start_invite_worker(self, emails: List[str]):
        """启动邀请工作线程"""
        # 这里应该启动实际的API调用工作线程
        # 为了演示，我们使用定时器模拟
        self.invite_timer = QTimer()
        self.invite_timer.timeout.connect(lambda: self.simulate_invite_progress(emails))
        self.invite_progress_value = 0
        self.invite_emails = emails
        self.invite_timer.start(100)

    def simulate_invite_progress(self, emails: List[str]):
        """模拟邀请进度"""
        self.invite_progress_value += 5
        self.invite_progress.setValue(self.invite_progress_value)

        if self.invite_progress_value >= 100:
            self.invite_timer.stop()
            self.invite_progress.setVisible(False)
            self.send_invite_btn.setEnabled(True)
            self.send_invite_btn.setText("📧 发送邀请")

            # 显示结果
            self.show_message("成功", f"成功发送 {len(emails)} 个邀请", "success")
            self.add_activity("✅", f"成功邀请 {len(emails)} 个成员", "刚刚")

            # 清空输入
            self.email_input.clear()

            # 更新邀请历史
            self.update_invite_history(emails)

    def start_refresh_worker(self):
        """启动刷新工作线程"""
        # 模拟数据刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.simulate_data_refresh)
        self.refresh_timer.setSingleShot(True)
        self.refresh_timer.start(1000)

    def simulate_data_refresh(self):
        """模拟数据刷新"""
        # 更新统计数据
        import random

        total_members = random.randint(10, 50)
        pending_invites = random.randint(0, 10)
        today_invites = random.randint(0, 5)
        success_rate = random.randint(70, 95)

        self.total_members_card.value_label.setText(str(total_members))
        self.pending_invites_card.value_label.setText(str(pending_invites))
        self.today_invites_card.value_label.setText(str(today_invites))
        self.success_rate_card.value_label.setText(f"{success_rate}%")

        self.add_activity("📊", "数据刷新完成", "刚刚")
        self.show_message("成功", "团队数据刷新完成", "success")

    def update_invite_history(self, emails: List[str]):
        """更新邀请历史"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

        for email in emails:
            row_count = self.invite_history_table.rowCount()
            self.invite_history_table.insertRow(row_count)

            self.invite_history_table.setItem(row_count, 0, QTableWidgetItem(email))
            self.invite_history_table.setItem(row_count, 1, QTableWidgetItem("已发送"))
            self.invite_history_table.setItem(row_count, 2, QTableWidgetItem(current_time))
            self.invite_history_table.setItem(row_count, 3, QTableWidgetItem("查看详情"))

    # 其他页面的创建方法（简化版）
    def create_manage_page(self) -> QWidget:
        """创建团队管理页面"""
        return self.create_simple_page("👥 团队管理", "团队成员管理功能正在开发中...")

    def create_batch_page(self) -> QWidget:
        """创建批量操作页面"""
        return self.create_simple_page("⚡ 批量操作", "批量操作功能正在开发中...")

    def create_data_page(self) -> QWidget:
        """创建数据分析页面"""
        return self.create_simple_page("📈 数据分析", "数据分析功能正在开发中...")

    def create_settings_page(self) -> QWidget:
        """创建设置页面"""
        return self.create_simple_page("⚙️ 设置", "系统设置功能正在开发中...")

    def create_simple_page(self, title: str, description: str) -> QWidget:
        """创建简单页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 页面描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 20px;
        """)
        layout.addWidget(desc_label)

        # 内容卡片
        content_card = QFrame()
        content_card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        card_layout = QVBoxLayout(content_card)
        card_layout.setContentsMargins(20, 20, 20, 20)

        # 卡片内容
        content_label = QLabel("功能正在开发中...")
        content_label.setStyleSheet("""
            font-size: 16px;
            color: #374151;
            text-align: center;
            padding: 40px;
        """)
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        card_layout.addWidget(content_label)
        layout.addWidget(content_card)
        layout.addStretch()

        return page


class SimpleModernUI(QMainWindow):
    """简化版现代化UI"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("团队管理工具 - 现代化界面")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 初始化UI
        self.init_ui()
        self.apply_styles()

    def set_api_client(self, api_client):
        """设置API客户端"""
        if hasattr(self, 'content_area'):
            self.content_area.set_api_client(api_client)
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = SimpleSidebar()
        self.sidebar.page_changed.connect(self.switch_page)
        
        # 创建内容区域
        self.content_area = SimpleContentArea()
        
        # 添加到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.content_area, 1)
        
        # 设置默认页面
        self.content_area.switch_to_page("dashboard")
    
    def switch_page(self, page_id: str):
        """切换页面"""
        self.content_area.switch_to_page(page_id)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: #f9fafb;
            }
        """)
