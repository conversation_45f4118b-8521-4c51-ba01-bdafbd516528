# -*- coding: utf-8 -*-
"""
简化版现代化UI
解决兼容性问题的简洁界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel, 
    QPushButton, QStackedWidget, QTextEdit, QTableWidget, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class SimpleSidebar(QFrame):
    """简化版侧边栏"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_page = "dashboard"
        self.buttons = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化侧边栏UI"""
        self.setFixedWidth(250)
        self.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border-right: 1px solid #e5e7eb;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo区域
        logo_area = self.create_logo_area()
        layout.addWidget(logo_area)
        
        # 导航菜单
        nav_area = self.create_navigation_area()
        layout.addWidget(nav_area)
        
        layout.addStretch()
    
    def create_logo_area(self) -> QWidget:
        """创建Logo区域"""
        container = QFrame()
        container.setFixedHeight(70)
        container.setStyleSheet("""
            QFrame {
                background: #2563eb;
                border: none;
            }
        """)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Logo和标题
        title_label = QLabel("🚀 团队管理")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        
        layout.addWidget(title_label)
        layout.addStretch()
        
        return container
    
    def create_navigation_area(self) -> QWidget:
        """创建导航区域"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 15, 0, 0)
        layout.setSpacing(2)
        
        # 导航项目
        nav_items = [
            ("dashboard", "📊 仪表盘"),
            ("invite", "✉️ 邀请成员"),
            ("manage", "👥 团队管理"),
            ("batch", "⚡ 批量操作"),
            ("data", "📈 数据分析"),
            ("settings", "⚙️ 设置")
        ]
        
        for page_id, title in nav_items:
            btn = self.create_nav_button(page_id, title)
            self.buttons[page_id] = btn
            layout.addWidget(btn)
        
        # 设置默认选中
        self.set_active_button("dashboard")
        
        return container
    
    def create_nav_button(self, page_id: str, title: str) -> QPushButton:
        """创建导航按钮"""
        btn = QPushButton(title)
        btn.setFixedHeight(45)
        btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 12px 20px;
                border: none;
                background: transparent;
                color: #111827;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: #f9fafb;
            }
            QPushButton[active="true"] {
                background: #dbeafe;
                color: #2563eb;
                border-right: 3px solid #2563eb;
                font-weight: bold;
            }
        """)
        
        btn.clicked.connect(lambda: self.switch_page(page_id))
        return btn
    
    def switch_page(self, page_id: str):
        """切换页面"""
        if page_id != self.current_page:
            self.set_active_button(page_id)
            self.current_page = page_id
            self.page_changed.emit(page_id)
    
    def set_active_button(self, page_id: str):
        """设置活动按钮"""
        for btn_id, btn in self.buttons.items():
            btn.setProperty("active", btn_id == page_id)
            btn.style().unpolish(btn)
            btn.style().polish(btn)


class SimpleContentArea(QStackedWidget):
    """简化版内容区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pages = {}
        self.init_pages()
    
    def init_pages(self):
        """初始化页面"""
        # 创建各个页面
        pages = [
            ("dashboard", "📊 仪表盘", "这里是仪表盘页面，显示团队概览信息。"),
            ("invite", "✉️ 邀请成员", "这里是邀请成员页面，可以发送邀请邮件。"),
            ("manage", "👥 团队管理", "这里是团队管理页面，管理团队成员。"),
            ("batch", "⚡ 批量操作", "这里是批量操作页面，执行批量任务。"),
            ("data", "📈 数据分析", "这里是数据分析页面，查看详细数据。"),
            ("settings", "⚙️ 设置", "这里是设置页面，配置系统参数。")
        ]
        
        for page_id, title, description in pages:
            page = self.create_simple_page(title, description)
            self.addWidget(page)
            self.pages[page_id] = page
    
    def create_simple_page(self, title: str, description: str) -> QWidget:
        """创建简单页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # 页面描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 20px;
        """)
        layout.addWidget(desc_label)
        
        # 内容卡片
        content_card = self.create_content_card()
        layout.addWidget(content_card)
        
        layout.addStretch()
        return page
    
    def create_content_card(self) -> QWidget:
        """创建内容卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 卡片内容
        content_label = QLabel("功能正在开发中...")
        content_label.setStyleSheet("""
            font-size: 16px;
            color: #374151;
            text-align: center;
            padding: 40px;
        """)
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addWidget(content_label)
        
        return card
    
    def switch_to_page(self, page_id: str):
        """切换到指定页面"""
        if page_id in self.pages:
            page_widget = self.pages[page_id]
            self.setCurrentWidget(page_widget)


class SimpleModernUI(QMainWindow):
    """简化版现代化UI"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("团队管理工具 - 现代化界面")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 初始化UI
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = SimpleSidebar()
        self.sidebar.page_changed.connect(self.switch_page)
        
        # 创建内容区域
        self.content_area = SimpleContentArea()
        
        # 添加到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.content_area, 1)
        
        # 设置默认页面
        self.content_area.switch_to_page("dashboard")
    
    def switch_page(self, page_id: str):
        """切换页面"""
        self.content_area.switch_to_page(page_id)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: #f9fafb;
            }
        """)
