# -*- coding: utf-8 -*-
"""
简化版现代化UI
解决兼容性问题的简洁界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel,
    QPushButton, QStackedWidget, QTextEdit, QTableWidget, QHeaderView,
    QTableWidgetItem, QProgressBar, QSpinBox, QCheckBox, QFileDialog,
    QMessageBox, QLineEdit, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor
import re
from typing import List, Dict, Any
from datetime import datetime


class SimpleSidebar(QFrame):
    """简化版侧边栏"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_page = "dashboard"
        self.buttons = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化侧边栏UI"""
        self.setFixedWidth(250)
        self.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border-right: 1px solid #e5e7eb;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo区域
        logo_area = self.create_logo_area()
        layout.addWidget(logo_area)
        
        # 导航菜单
        nav_area = self.create_navigation_area()
        layout.addWidget(nav_area)
        
        layout.addStretch()
    
    def create_logo_area(self) -> QWidget:
        """创建Logo区域"""
        container = QFrame()
        container.setFixedHeight(70)
        container.setStyleSheet("""
            QFrame {
                background: #2563eb;
                border: none;
            }
        """)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Logo和标题
        title_label = QLabel("🚀 团队管理")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        
        layout.addWidget(title_label)
        layout.addStretch()
        
        return container
    
    def create_navigation_area(self) -> QWidget:
        """创建导航区域"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 15, 0, 0)
        layout.setSpacing(2)
        
        # 导航项目
        nav_items = [
            ("dashboard", "📊 仪表盘"),
            ("invite", "✉️ 邀请成员"),
            ("manage", "👥 团队管理"),
            ("batch", "⚡ 批量操作"),
            ("data", "📈 数据分析"),
            ("settings", "⚙️ 设置")
        ]
        
        for page_id, title in nav_items:
            btn = self.create_nav_button(page_id, title)
            self.buttons[page_id] = btn
            layout.addWidget(btn)
        
        # 设置默认选中
        self.set_active_button("dashboard")
        
        return container
    
    def create_nav_button(self, page_id: str, title: str) -> QPushButton:
        """创建导航按钮"""
        btn = QPushButton(title)
        btn.setFixedHeight(45)
        btn.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 12px 20px;
                border: none;
                background: transparent;
                color: #111827;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: #f9fafb;
            }
            QPushButton[active="true"] {
                background: #dbeafe;
                color: #2563eb;
                border-right: 3px solid #2563eb;
                font-weight: bold;
            }
        """)
        
        btn.clicked.connect(lambda: self.switch_page(page_id))
        return btn
    
    def switch_page(self, page_id: str):
        """切换页面"""
        if page_id != self.current_page:
            self.set_active_button(page_id)
            self.current_page = page_id
            self.page_changed.emit(page_id)
    
    def set_active_button(self, page_id: str):
        """设置活动按钮"""
        for btn_id, btn in self.buttons.items():
            btn.setProperty("active", btn_id == page_id)
            btn.style().unpolish(btn)
            btn.style().polish(btn)


class SimpleContentArea(QStackedWidget):
    """简化版内容区域"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.pages = {}
        self.api_client = None
        self.team_data = None
        self.worker_thread = None
        self.init_pages()
    
    def init_pages(self):
        """初始化页面"""
        # 仪表盘页面
        dashboard_page = self.create_dashboard_page()
        self.addWidget(dashboard_page)
        self.pages["dashboard"] = dashboard_page

        # 邀请成员页面
        invite_page = self.create_invite_page()
        self.addWidget(invite_page)
        self.pages["invite"] = invite_page

        # 团队管理页面
        manage_page = self.create_manage_page()
        self.addWidget(manage_page)
        self.pages["manage"] = manage_page

        # 批量操作页面
        batch_page = self.create_batch_page()
        self.addWidget(batch_page)
        self.pages["batch"] = batch_page

        # 数据分析页面
        data_page = self.create_data_page()
        self.addWidget(data_page)
        self.pages["data"] = data_page

        # 设置页面
        settings_page = self.create_settings_page()
        self.addWidget(settings_page)
        self.pages["settings"] = settings_page
    
    def create_dashboard_page(self) -> QWidget:
        """创建仪表盘页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("📊 仪表盘")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 统计卡片区域
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)

        # 创建统计卡片
        self.total_members_card = self.create_stat_card("👥", "团队成员", "0")
        self.pending_invites_card = self.create_stat_card("📧", "待处理邀请", "0")
        self.today_invites_card = self.create_stat_card("✅", "今日邀请", "0")
        self.success_rate_card = self.create_stat_card("📊", "成功率", "0%")

        stats_layout.addWidget(self.total_members_card)
        stats_layout.addWidget(self.pending_invites_card)
        stats_layout.addWidget(self.today_invites_card)
        stats_layout.addWidget(self.success_rate_card)

        layout.addLayout(stats_layout)

        # 快速操作区域
        quick_actions_card = self.create_quick_actions_card()
        layout.addWidget(quick_actions_card)

        # 最近活动区域
        recent_activity_card = self.create_recent_activity_card()
        layout.addWidget(recent_activity_card)

        layout.addStretch()
        return page

    def create_stat_card(self, icon: str, title: str, value: str) -> QFrame:
        """创建统计卡片"""
        card = QFrame()
        card.setFixedHeight(80)
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                border-left: 4px solid #2563eb;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            font-size: 24px;
            color: #2563eb;
            min-width: 40px;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 内容
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)

        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
        """)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 12px;
            color: #6b7280;
        """)

        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)

        layout.addWidget(icon_label)
        layout.addLayout(content_layout)
        layout.addStretch()

        # 保存值标签以便更新
        card.value_label = value_label

        return card

    def create_quick_actions_card(self) -> QFrame:
        """创建快速操作卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("⚡ 快速操作")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 快速操作按钮
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.setStyleSheet(self.get_button_style("primary"))
        refresh_btn.clicked.connect(self.refresh_team_data)

        invite_btn = QPushButton("📧 邀请成员")
        invite_btn.setStyleSheet(self.get_button_style("success"))
        invite_btn.clicked.connect(lambda: self.switch_to_page("invite"))

        manage_btn = QPushButton("👥 团队管理")
        manage_btn.setStyleSheet(self.get_button_style("info"))
        manage_btn.clicked.connect(lambda: self.switch_to_page("manage"))

        settings_btn = QPushButton("⚙️ 设置")
        settings_btn.setStyleSheet(self.get_button_style("secondary"))
        settings_btn.clicked.connect(lambda: self.switch_to_page("settings"))

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(invite_btn)
        button_layout.addWidget(manage_btn)
        button_layout.addWidget(settings_btn)

        layout.addLayout(button_layout)

        return card

    def create_recent_activity_card(self) -> QFrame:
        """创建最近活动卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📋 最近活动")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # 活动列表
        self.activity_list = QWidget()
        activity_layout = QVBoxLayout(self.activity_list)
        activity_layout.setSpacing(8)

        # 默认活动
        self.add_activity("🚀", "应用启动", "刚刚")

        layout.addWidget(self.activity_list)

        return card

    def add_activity(self, icon: str, text: str, time: str):
        """添加活动记录"""
        activity_item = QFrame()
        activity_item.setStyleSheet("""
            QFrame {
                background: #f9fafb;
                border-radius: 6px;
                padding: 8px;
            }
        """)

        layout = QHBoxLayout(activity_item)
        layout.setContentsMargins(12, 8, 12, 8)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 14px;")

        # 文本
        text_label = QLabel(text)
        text_label.setStyleSheet("color: #374151; font-weight: 500;")

        # 时间
        time_label = QLabel(time)
        time_label.setStyleSheet("color: #6b7280; font-size: 12px;")

        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()
        layout.addWidget(time_label)

        # 添加到活动列表
        self.activity_list.layout().addWidget(activity_item)

    def create_simple_page(self, title: str, description: str) -> QWidget:
        """创建简单页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # 页面描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 20px;
        """)
        layout.addWidget(desc_label)
        
        # 内容卡片
        content_card = self.create_content_card()
        layout.addWidget(content_card)
        
        layout.addStretch()
        return page
    
    def create_content_card(self) -> QWidget:
        """创建内容卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 卡片内容
        content_label = QLabel("功能正在开发中...")
        content_label.setStyleSheet("""
            font-size: 16px;
            color: #374151;
            text-align: center;
            padding: 40px;
        """)
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addWidget(content_label)
        
        return card

    def create_invite_page(self) -> QWidget:
        """创建邀请成员页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("✉️ 邀请成员")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 邀请表单卡片
        invite_form_card = self.create_invite_form_card()
        layout.addWidget(invite_form_card)

        # 邀请历史卡片
        invite_history_card = self.create_invite_history_card()
        layout.addWidget(invite_history_card)

        layout.addStretch()
        return page

    def create_invite_form_card(self) -> QFrame:
        """创建邀请表单卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("📧 发送邀请")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 邮箱输入区域
        email_label = QLabel("邮箱地址（每行一个）:")
        email_label.setStyleSheet("color: #374151; font-weight: 500;")
        layout.addWidget(email_label)

        self.email_input = QTextEdit()
        self.email_input.setFixedHeight(120)
        self.email_input.setPlaceholderText("请输入邮箱地址，每行一个\n例如：\<EMAIL>\<EMAIL>")
        self.email_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                background: #ffffff;
            }
            QTextEdit:focus {
                border-color: #2563eb;
            }
        """)
        layout.addWidget(self.email_input)

        # 进度条
        self.invite_progress = QProgressBar()
        self.invite_progress.setVisible(False)
        self.invite_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e5e7eb;
                border-radius: 4px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #2563eb;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.invite_progress)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 发送邀请按钮
        self.send_invite_btn = QPushButton("📧 发送邀请")
        self.send_invite_btn.setStyleSheet(self.get_button_style("primary"))
        self.send_invite_btn.clicked.connect(self.send_invitations)

        # 清空按钮
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.setStyleSheet(self.get_button_style("secondary"))
        clear_btn.clicked.connect(self.clear_email_input)

        # 导入文件按钮
        import_btn = QPushButton("📁 导入文件")
        import_btn.setStyleSheet(self.get_button_style("info"))
        import_btn.clicked.connect(self.import_email_file)

        button_layout.addWidget(self.send_invite_btn)
        button_layout.addWidget(clear_btn)
        button_layout.addWidget(import_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        return card

    def create_invite_history_card(self) -> QFrame:
        """创建邀请历史卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📋 邀请历史")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # 邀请历史表格
        self.invite_history_table = QTableWidget()
        self.invite_history_table.setColumnCount(4)
        self.invite_history_table.setHorizontalHeaderLabels(["邮箱", "状态", "邀请时间", "操作"])

        # 设置表格样式
        self.invite_history_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                background: #ffffff;
                gridline-color: #f3f4f6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f3f4f6;
            }
            QHeaderView::section {
                background: #f9fafb;
                padding: 10px;
                border: none;
                font-weight: bold;
                color: #374151;
            }
        """)

        # 设置表格属性
        header = self.invite_history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        self.invite_history_table.setAlternatingRowColors(True)
        self.invite_history_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        layout.addWidget(self.invite_history_table)

        return card

    def switch_to_page(self, page_id: str):
        """切换到指定页面"""
        if page_id in self.pages:
            page_widget = self.pages[page_id]
            self.setCurrentWidget(page_widget)

    def set_api_client(self, api_client):
        """设置API客户端"""
        self.api_client = api_client

    def get_button_style(self, button_type: str) -> str:
        """获取按钮样式"""
        styles = {
            "primary": """
                QPushButton {
                    background: #2563eb;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #1d4ed8;
                }
                QPushButton:pressed {
                    background: #1e40af;
                }
                QPushButton:disabled {
                    background: #9ca3af;
                }
            """,
            "success": """
                QPushButton {
                    background: #10b981;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #059669;
                }
                QPushButton:pressed {
                    background: #047857;
                }
            """,
            "info": """
                QPushButton {
                    background: #06b6d4;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #0891b2;
                }
                QPushButton:pressed {
                    background: #0e7490;
                }
            """,
            "secondary": """
                QPushButton {
                    background: #6b7280;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #4b5563;
                }
                QPushButton:pressed {
                    background: #374151;
                }
            """,
            "danger": """
                QPushButton {
                    background: #ef4444;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-weight: 500;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #dc2626;
                }
                QPushButton:pressed {
                    background: #b91c1c;
                }
            """
        }
        return styles.get(button_type, styles["primary"])

    def validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def send_invitations(self):
        """发送邀请"""
        if not self.api_client:
            self.show_message("错误", "API客户端未初始化", "error")
            return

        text = self.email_input.toPlainText().strip()
        if not text:
            self.show_message("提示", "请输入邮箱地址", "warning")
            return

        # 解析邮箱列表
        emails = [email.strip() for email in text.split('\n') if email.strip()]
        valid_emails = [email for email in emails if self.validate_email(email)]
        invalid_emails = [email for email in emails if not self.validate_email(email)]

        if invalid_emails:
            self.show_message("警告", f"发现 {len(invalid_emails)} 个无效邮箱，将被忽略", "warning")

        if not valid_emails:
            self.show_message("错误", "没有找到有效的邮箱地址", "error")
            return

        # 显示进度条
        self.invite_progress.setVisible(True)
        self.invite_progress.setValue(0)
        self.send_invite_btn.setEnabled(False)
        self.send_invite_btn.setText("发送中...")

        # 启动工作线程
        self.start_invite_worker(valid_emails)

        # 添加活动记录
        self.add_activity("📧", f"开始邀请 {len(valid_emails)} 个成员", "刚刚")

    def clear_email_input(self):
        """清空邮箱输入"""
        self.email_input.clear()

    def import_email_file(self):
        """导入邮箱文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择邮箱文件", "",
            "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 解析文件内容
                emails = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and '@' in line:
                        # 简单的邮箱提取
                        if ',' in line:
                            emails.extend([email.strip() for email in line.split(',')])
                        else:
                            emails.append(line)

                # 添加到输入框
                current_text = self.email_input.toPlainText()
                if current_text:
                    current_text += '\n'
                current_text += '\n'.join(emails)
                self.email_input.setPlainText(current_text)

                self.show_message("成功", f"成功导入 {len(emails)} 个邮箱地址", "success")

            except Exception as e:
                self.show_message("错误", f"文件导入失败: {str(e)}", "error")

    def refresh_team_data(self):
        """刷新团队数据"""
        if not self.api_client:
            self.show_message("错误", "API客户端未初始化", "error")
            return

        # 启动数据刷新工作线程
        self.start_refresh_worker()
        self.add_activity("🔄", "刷新团队数据", "刚刚")

    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """显示消息"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        if msg_type == "error":
            msg_box.setIcon(QMessageBox.Icon.Critical)
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Icon.Warning)
        elif msg_type == "success":
            msg_box.setIcon(QMessageBox.Icon.Information)
        else:
            msg_box.setIcon(QMessageBox.Icon.Information)

        msg_box.exec()

    def start_invite_worker(self, emails: List[str]):
        """启动邀请工作线程"""
        # 这里应该启动实际的API调用工作线程
        # 为了演示，我们使用定时器模拟
        self.invite_timer = QTimer()
        self.invite_timer.timeout.connect(lambda: self.simulate_invite_progress(emails))
        self.invite_progress_value = 0
        self.invite_emails = emails
        self.invite_timer.start(100)

    def simulate_invite_progress(self, emails: List[str]):
        """模拟邀请进度"""
        self.invite_progress_value += 5
        self.invite_progress.setValue(self.invite_progress_value)

        if self.invite_progress_value >= 100:
            self.invite_timer.stop()
            self.invite_progress.setVisible(False)
            self.send_invite_btn.setEnabled(True)
            self.send_invite_btn.setText("📧 发送邀请")

            # 显示结果
            self.show_message("成功", f"成功发送 {len(emails)} 个邀请", "success")
            self.add_activity("✅", f"成功邀请 {len(emails)} 个成员", "刚刚")

            # 清空输入
            self.email_input.clear()

            # 更新邀请历史
            self.update_invite_history(emails)

    def start_refresh_worker(self):
        """启动刷新工作线程"""
        # 模拟数据刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.simulate_data_refresh)
        self.refresh_timer.setSingleShot(True)
        self.refresh_timer.start(1000)

    def simulate_data_refresh(self):
        """模拟数据刷新"""
        # 更新统计数据
        import random

        total_members = random.randint(10, 50)
        pending_invites = random.randint(0, 10)
        today_invites = random.randint(0, 5)
        success_rate = random.randint(70, 95)

        self.total_members_card.value_label.setText(str(total_members))
        self.pending_invites_card.value_label.setText(str(pending_invites))
        self.today_invites_card.value_label.setText(str(today_invites))
        self.success_rate_card.value_label.setText(f"{success_rate}%")

        self.add_activity("📊", "数据刷新完成", "刚刚")
        self.show_message("成功", "团队数据刷新完成", "success")

    def update_invite_history(self, emails: List[str]):
        """更新邀请历史"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

        for email in emails:
            row_count = self.invite_history_table.rowCount()
            self.invite_history_table.insertRow(row_count)

            self.invite_history_table.setItem(row_count, 0, QTableWidgetItem(email))
            self.invite_history_table.setItem(row_count, 1, QTableWidgetItem("已发送"))
            self.invite_history_table.setItem(row_count, 2, QTableWidgetItem(current_time))
            self.invite_history_table.setItem(row_count, 3, QTableWidgetItem("查看详情"))

    # 其他页面的创建方法（简化版）
    def create_manage_page(self) -> QWidget:
        """创建团队管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("👥 团队管理")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 团队信息卡片
        team_info_card = self.create_team_info_card()
        layout.addWidget(team_info_card)

        # 搜索和筛选区域
        search_filter_card = self.create_search_filter_card()
        layout.addWidget(search_filter_card)

        # 成员列表卡片
        members_list_card = self.create_members_list_card()
        layout.addWidget(members_list_card)

        layout.addStretch()
        return page

    def create_team_info_card(self) -> QFrame:
        """创建团队信息卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("ℹ️ 团队信息")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 信息布局
        info_layout = QHBoxLayout()
        info_layout.setSpacing(30)

        # 统计信息
        stats_layout = QVBoxLayout()

        # 创建统计项目
        self.team_total_label = self.create_info_item("总成员数", "0")
        self.team_active_label = self.create_info_item("活跃成员", "0")
        self.team_pending_label = self.create_info_item("待处理邀请", "0")
        self.team_today_label = self.create_info_item("今日新增", "0")

        stats_layout.addWidget(self.team_total_label)
        stats_layout.addWidget(self.team_active_label)
        stats_layout.addWidget(self.team_pending_label)
        stats_layout.addWidget(self.team_today_label)

        # 操作按钮
        actions_layout = QVBoxLayout()
        actions_layout.setSpacing(10)

        refresh_team_btn = QPushButton("🔄 刷新团队数据")
        refresh_team_btn.setStyleSheet(self.get_button_style("primary"))
        refresh_team_btn.clicked.connect(self.refresh_team_data)

        export_team_btn = QPushButton("📊 导出团队数据")
        export_team_btn.setStyleSheet(self.get_button_style("success"))
        export_team_btn.clicked.connect(self.export_team_data)

        invite_more_btn = QPushButton("📧 邀请更多成员")
        invite_more_btn.setStyleSheet(self.get_button_style("info"))
        invite_more_btn.clicked.connect(lambda: self.switch_to_page("invite"))

        actions_layout.addWidget(refresh_team_btn)
        actions_layout.addWidget(export_team_btn)
        actions_layout.addWidget(invite_more_btn)
        actions_layout.addStretch()

        info_layout.addLayout(stats_layout, 2)
        info_layout.addLayout(actions_layout, 1)

        layout.addLayout(info_layout)

        return card

    def create_info_item(self, label: str, value: str) -> QWidget:
        """创建信息项目"""
        item_widget = QWidget()
        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(0, 5, 0, 5)

        label_widget = QLabel(label + ":")
        label_widget.setStyleSheet("color: #6b7280; font-weight: 500;")

        value_widget = QLabel(value)
        value_widget.setStyleSheet("color: #2563eb; font-weight: bold; font-size: 16px;")

        item_layout.addWidget(label_widget)
        item_layout.addStretch()
        item_layout.addWidget(value_widget)

        # 保存值标签以便更新
        item_widget.value_label = value_widget

        return item_widget

    def create_search_filter_card(self) -> QFrame:
        """创建搜索和筛选卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🔍 搜索和筛选")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 搜索和筛选控件
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)

        # 搜索框
        search_label = QLabel("搜索:")
        search_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入邮箱或姓名搜索...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                background: #ffffff;
            }
            QLineEdit:focus {
                border-color: #2563eb;
            }
        """)
        self.search_input.textChanged.connect(self.filter_members)

        # 状态筛选
        status_label = QLabel("状态:")
        status_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "已加入", "待处理", "邀请中"])
        self.status_filter.setStyleSheet("""
            QComboBox {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                background: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #2563eb;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)
        self.status_filter.currentTextChanged.connect(self.filter_members)

        # 批量操作按钮
        batch_select_btn = QPushButton("☑️ 批量选择")
        batch_select_btn.setStyleSheet(self.get_button_style("secondary"))
        batch_select_btn.clicked.connect(self.toggle_batch_mode)

        controls_layout.addWidget(search_label)
        controls_layout.addWidget(self.search_input, 2)
        controls_layout.addWidget(status_label)
        controls_layout.addWidget(self.status_filter)
        controls_layout.addWidget(batch_select_btn)
        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        return card

    def create_members_list_card(self) -> QFrame:
        """创建成员列表卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题和批量操作
        header_layout = QHBoxLayout()

        title_label = QLabel("👥 成员列表")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
        """)

        # 批量操作按钮（默认隐藏）
        self.batch_actions_widget = QWidget()
        batch_actions_layout = QHBoxLayout(self.batch_actions_widget)
        batch_actions_layout.setContentsMargins(0, 0, 0, 0)
        batch_actions_layout.setSpacing(10)

        batch_delete_btn = QPushButton("🗑️ 批量删除")
        batch_delete_btn.setStyleSheet(self.get_button_style("danger"))
        batch_delete_btn.clicked.connect(self.batch_delete_members)

        batch_plan_btn = QPushButton("⭐ 批量切换计划")
        batch_plan_btn.setStyleSheet(self.get_button_style("warning"))
        batch_plan_btn.clicked.connect(self.batch_change_plan)

        batch_actions_layout.addWidget(batch_delete_btn)
        batch_actions_layout.addWidget(batch_plan_btn)

        self.batch_actions_widget.setVisible(False)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.batch_actions_widget)

        layout.addLayout(header_layout)

        # 成员表格
        self.members_table = QTableWidget()
        self.members_table.setColumnCount(6)
        self.members_table.setHorizontalHeaderLabels(["选择", "邮箱/姓名", "状态", "加入时间", "计划", "操作"])

        # 设置表格样式
        self.members_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                background: #ffffff;
                gridline-color: #f3f4f6;
                selection-background-color: #dbeafe;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #f3f4f6;
            }
            QHeaderView::section {
                background: #f9fafb;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                color: #374151;
                border-bottom: 2px solid #e5e7eb;
            }
        """)

        # 设置表格属性
        header = self.members_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 选择列
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)           # 邮箱列
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 状态列
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 时间列
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 计划列
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 操作列

        self.members_table.setAlternatingRowColors(True)
        self.members_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 初始化示例数据
        self.load_sample_members_data()

        layout.addWidget(self.members_table)

        return card

    def load_sample_members_data(self):
        """加载示例成员数据"""
        sample_members = [
            ("<EMAIL>", "已加入", "2024-01-15 10:30", "社区计划"),
            ("<EMAIL>", "待处理", "2024-01-16 14:20", "最大计划"),
            ("<EMAIL>", "已加入", "2024-01-17 09:15", "社区计划"),
            ("<EMAIL>", "邀请中", "2024-01-18 16:45", "社区计划"),
            ("<EMAIL>", "已加入", "2024-01-19 11:30", "最大计划"),
        ]

        self.members_table.setRowCount(len(sample_members))

        for row, (email, status, join_time, plan) in enumerate(sample_members):
            # 选择框
            checkbox = QCheckBox()
            checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
            self.members_table.setCellWidget(row, 0, checkbox)

            # 邮箱
            self.members_table.setItem(row, 1, QTableWidgetItem(email))

            # 状态
            status_item = QTableWidgetItem(status)
            if status == "已加入":
                status_item.setForeground(QColor("#10b981"))
            elif status == "待处理":
                status_item.setForeground(QColor("#f59e0b"))
            elif status == "邀请中":
                status_item.setForeground(QColor("#06b6d4"))
            self.members_table.setItem(row, 2, status_item)

            # 加入时间
            self.members_table.setItem(row, 3, QTableWidgetItem(join_time))

            # 计划
            plan_item = QTableWidgetItem(plan)
            if plan == "最大计划":
                plan_item.setForeground(QColor("#f59e0b"))
            else:
                plan_item.setForeground(QColor("#6b7280"))
            self.members_table.setItem(row, 4, plan_item)

            # 操作按钮
            actions_widget = self.create_member_actions_widget(row)
            self.members_table.setCellWidget(row, 5, actions_widget)

    def create_member_actions_widget(self, row: int) -> QWidget:
        """创建成员操作按钮组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 删除按钮
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 30)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: #ef4444;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: #dc2626;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_member(row))

        # 切换计划按钮
        plan_btn = QPushButton("⭐")
        plan_btn.setFixedSize(30, 30)
        plan_btn.setStyleSheet("""
            QPushButton {
                background: #f59e0b;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: #d97706;
            }
        """)
        plan_btn.clicked.connect(lambda: self.change_member_plan(row))

        # 详情按钮
        detail_btn = QPushButton("👁️")
        detail_btn.setFixedSize(30, 30)
        detail_btn.setStyleSheet("""
            QPushButton {
                background: #06b6d4;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: #0891b2;
            }
        """)
        detail_btn.clicked.connect(lambda: self.show_member_details(row))

        layout.addWidget(delete_btn)
        layout.addWidget(plan_btn)
        layout.addWidget(detail_btn)

        return widget

    def filter_members(self):
        """筛选成员列表"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()

        for row in range(self.members_table.rowCount()):
            # 获取邮箱和状态
            email_item = self.members_table.item(row, 1)
            status_item = self.members_table.item(row, 2)

            if email_item and status_item:
                email = email_item.text().lower()
                status = status_item.text()

                # 检查搜索条件
                search_match = search_text in email if search_text else True
                status_match = (status_filter == "全部" or status == status_filter)

                # 显示或隐藏行
                self.members_table.setRowHidden(row, not (search_match and status_match))

    def toggle_batch_mode(self):
        """切换批量选择模式"""
        is_visible = self.batch_actions_widget.isVisible()
        self.batch_actions_widget.setVisible(not is_visible)

        # 显示/隐藏选择列
        self.members_table.setColumnHidden(0, is_visible)

        if not is_visible:
            self.add_activity("☑️", "启用批量选择模式", "刚刚")
        else:
            self.add_activity("❌", "退出批量选择模式", "刚刚")

    def get_selected_rows(self) -> List[int]:
        """获取选中的行"""
        selected_rows = []
        for row in range(self.members_table.rowCount()):
            checkbox = self.members_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_rows.append(row)
        return selected_rows

    def batch_delete_members(self):
        """批量删除成员"""
        selected_rows = self.get_selected_rows()
        if not selected_rows:
            self.show_message("提示", "请先选择要删除的成员", "warning")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除选中的 {len(selected_rows)} 个成员吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从后往前删除，避免索引问题
            for row in sorted(selected_rows, reverse=True):
                self.members_table.removeRow(row)

            self.show_message("成功", f"成功删除 {len(selected_rows)} 个成员", "success")
            self.add_activity("🗑️", f"批量删除 {len(selected_rows)} 个成员", "刚刚")

            # 更新统计信息
            self.update_team_stats()

    def batch_change_plan(self):
        """批量切换计划"""
        selected_rows = self.get_selected_rows()
        if not selected_rows:
            self.show_message("提示", "请先选择要切换计划的成员", "warning")
            return

        # 计划选择对话框
        from PyQt6.QtWidgets import QInputDialog
        plans = ["社区计划", "最大计划"]
        plan, ok = QInputDialog.getItem(
            self, "选择计划", "请选择要切换到的计划:", plans, 0, False
        )

        if ok and plan:
            for row in selected_rows:
                plan_item = QTableWidgetItem(plan)
                if plan == "最大计划":
                    plan_item.setForeground(QColor("#f59e0b"))
                else:
                    plan_item.setForeground(QColor("#6b7280"))
                self.members_table.setItem(row, 4, plan_item)

            self.show_message("成功", f"成功为 {len(selected_rows)} 个成员切换到{plan}", "success")
            self.add_activity("⭐", f"批量切换 {len(selected_rows)} 个成员到{plan}", "刚刚")

    def delete_member(self, row: int):
        """删除单个成员"""
        email_item = self.members_table.item(row, 1)
        if email_item:
            email = email_item.text()

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除成员 {email} 吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.members_table.removeRow(row)
                self.show_message("成功", f"成功删除成员 {email}", "success")
                self.add_activity("🗑️", f"删除成员 {email}", "刚刚")
                self.update_team_stats()

    def change_member_plan(self, row: int):
        """切换成员计划"""
        email_item = self.members_table.item(row, 1)
        current_plan_item = self.members_table.item(row, 4)

        if email_item and current_plan_item:
            email = email_item.text()
            current_plan = current_plan_item.text()

            # 切换到另一个计划
            new_plan = "最大计划" if current_plan == "社区计划" else "社区计划"

            plan_item = QTableWidgetItem(new_plan)
            if new_plan == "最大计划":
                plan_item.setForeground(QColor("#f59e0b"))
            else:
                plan_item.setForeground(QColor("#6b7280"))

            self.members_table.setItem(row, 4, plan_item)

            self.show_message("成功", f"成功将 {email} 切换到{new_plan}", "success")
            self.add_activity("⭐", f"切换 {email} 到{new_plan}", "刚刚")

    def show_member_details(self, row: int):
        """显示成员详情"""
        email_item = self.members_table.item(row, 1)
        status_item = self.members_table.item(row, 2)
        time_item = self.members_table.item(row, 3)
        plan_item = self.members_table.item(row, 4)

        if all([email_item, status_item, time_item, plan_item]):
            details = f"""
成员详情信息：

邮箱: {email_item.text()}
状态: {status_item.text()}
加入时间: {time_item.text()}
当前计划: {plan_item.text()}

操作历史:
- 邀请发送时间: {time_item.text()}
- 最后活跃时间: 2024-01-20 15:30
- 计划变更次数: 1
            """

            self.show_message("成员详情", details.strip(), "info")

    def export_team_data(self):
        """导出团队数据"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出团队数据", "team_data.csv",
                "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
            )

            if file_path:
                # 模拟导出过程
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # 写入表头
                    headers = ["邮箱", "状态", "加入时间", "计划"]
                    writer.writerow(headers)

                    # 写入数据
                    for row in range(self.members_table.rowCount()):
                        if not self.members_table.isRowHidden(row):
                            row_data = []
                            for col in range(1, 5):  # 跳过选择列
                                item = self.members_table.item(row, col)
                                row_data.append(item.text() if item else "")
                            writer.writerow(row_data)

                self.show_message("成功", f"团队数据已导出到: {file_path}", "success")
                self.add_activity("📊", "导出团队数据", "刚刚")

        except Exception as e:
            self.show_message("错误", f"导出失败: {str(e)}", "error")

    def update_team_stats(self):
        """更新团队统计信息"""
        total_members = self.members_table.rowCount()
        active_members = 0
        pending_members = 0

        for row in range(total_members):
            status_item = self.members_table.item(row, 2)
            if status_item:
                status = status_item.text()
                if status == "已加入":
                    active_members += 1
                elif status in ["待处理", "邀请中"]:
                    pending_members += 1

        # 更新统计显示
        self.team_total_label.value_label.setText(str(total_members))
        self.team_active_label.value_label.setText(str(active_members))
        self.team_pending_label.value_label.setText(str(pending_members))

        # 更新仪表盘统计
        self.total_members_card.value_label.setText(str(total_members))
        self.pending_invites_card.value_label.setText(str(pending_members))

    def create_batch_page(self) -> QWidget:
        """创建批量操作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("⚡ 批量操作")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 批量邀请卡片
        batch_invite_card = self.create_batch_invite_card()
        layout.addWidget(batch_invite_card)

        # 批量管理卡片
        batch_manage_card = self.create_batch_manage_card()
        layout.addWidget(batch_manage_card)

        # 批量导出卡片
        batch_export_card = self.create_batch_export_card()
        layout.addWidget(batch_export_card)

        layout.addStretch()
        return page

    def create_batch_invite_card(self) -> QFrame:
        """创建批量邀请卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("📧 批量邀请")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 文件导入区域
        import_layout = QHBoxLayout()
        import_layout.setSpacing(15)

        import_label = QLabel("导入邮箱文件:")
        import_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.batch_file_path_label = QLabel("未选择文件")
        self.batch_file_path_label.setStyleSheet("color: #6b7280; font-style: italic;")

        select_file_btn = QPushButton("📁 选择文件")
        select_file_btn.setStyleSheet(self.get_button_style("info"))
        select_file_btn.clicked.connect(self.select_batch_file)

        import_layout.addWidget(import_label)
        import_layout.addWidget(self.batch_file_path_label, 1)
        import_layout.addWidget(select_file_btn)

        layout.addLayout(import_layout)

        # 邀请设置
        settings_layout = QHBoxLayout()
        settings_layout.setSpacing(20)

        # 邀请间隔设置
        interval_label = QLabel("邀请间隔(秒):")
        interval_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.invite_interval = QSpinBox()
        self.invite_interval.setRange(1, 60)
        self.invite_interval.setValue(2)
        self.invite_interval.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px;
                background: #ffffff;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #2563eb;
            }
        """)

        # 批量大小设置
        batch_size_label = QLabel("批量大小:")
        batch_size_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.batch_size = QSpinBox()
        self.batch_size.setRange(1, 100)
        self.batch_size.setValue(10)
        self.batch_size.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px;
                background: #ffffff;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #2563eb;
            }
        """)

        settings_layout.addWidget(interval_label)
        settings_layout.addWidget(self.invite_interval)
        settings_layout.addWidget(batch_size_label)
        settings_layout.addWidget(self.batch_size)
        settings_layout.addStretch()

        layout.addLayout(settings_layout)

        # 批量邀请按钮
        batch_invite_btn = QPushButton("📧 开始批量邀请")
        batch_invite_btn.setStyleSheet(self.get_button_style("primary"))
        batch_invite_btn.clicked.connect(self.start_batch_invite)

        layout.addWidget(batch_invite_btn)

        # 进度显示
        self.batch_progress = QProgressBar()
        self.batch_progress.setVisible(False)
        self.batch_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e5e7eb;
                border-radius: 4px;
                text-align: center;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #2563eb;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.batch_progress)

        return card

    def create_batch_manage_card(self) -> QFrame:
        """创建批量管理卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("⚙️ 批量管理")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 计划管理区域
        plan_layout = QHBoxLayout()
        plan_layout.setSpacing(15)

        plan_label = QLabel("计划管理:")
        plan_label.setStyleSheet("color: #374151; font-weight: 500;")

        community_plan_btn = QPushButton("👥 批量设为社区计划")
        community_plan_btn.setStyleSheet(self.get_button_style("secondary"))
        community_plan_btn.clicked.connect(lambda: self.batch_set_plan("社区计划"))

        max_plan_btn = QPushButton("⭐ 批量设为最大计划")
        max_plan_btn.setStyleSheet(self.get_button_style("warning"))
        max_plan_btn.clicked.connect(lambda: self.batch_set_plan("最大计划"))

        plan_layout.addWidget(plan_label)
        plan_layout.addWidget(community_plan_btn)
        plan_layout.addWidget(max_plan_btn)
        plan_layout.addStretch()

        layout.addLayout(plan_layout)

        # 清理操作区域
        cleanup_layout = QHBoxLayout()
        cleanup_layout.setSpacing(15)

        cleanup_label = QLabel("清理操作:")
        cleanup_label.setStyleSheet("color: #374151; font-weight: 500;")

        delete_pending_btn = QPushButton("🗑️ 删除待处理邀请")
        delete_pending_btn.setStyleSheet(self.get_button_style("danger"))
        delete_pending_btn.clicked.connect(self.delete_pending_invites)

        delete_failed_btn = QPushButton("❌ 删除失败邀请")
        delete_failed_btn.setStyleSheet(self.get_button_style("danger"))
        delete_failed_btn.clicked.connect(self.delete_failed_invites)

        cleanup_layout.addWidget(cleanup_label)
        cleanup_layout.addWidget(delete_pending_btn)
        cleanup_layout.addWidget(delete_failed_btn)
        cleanup_layout.addStretch()

        layout.addLayout(cleanup_layout)

        return card

    def create_batch_export_card(self) -> QFrame:
        """创建批量导出卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("📊 批量导出")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 导出选项
        export_layout = QHBoxLayout()
        export_layout.setSpacing(15)

        export_all_btn = QPushButton("📋 导出所有成员")
        export_all_btn.setStyleSheet(self.get_button_style("success"))
        export_all_btn.clicked.connect(lambda: self.export_members("all"))

        export_pending_btn = QPushButton("⏳ 导出待处理邀请")
        export_pending_btn.setStyleSheet(self.get_button_style("warning"))
        export_pending_btn.clicked.connect(lambda: self.export_members("pending"))

        export_active_btn = QPushButton("✅ 导出活跃成员")
        export_active_btn.setStyleSheet(self.get_button_style("info"))
        export_active_btn.clicked.connect(lambda: self.export_members("active"))

        export_layout.addWidget(export_all_btn)
        export_layout.addWidget(export_pending_btn)
        export_layout.addWidget(export_active_btn)
        export_layout.addStretch()

        layout.addLayout(export_layout)

        return card

    # 批量操作功能方法
    def select_batch_file(self):
        """选择批量邀请文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择邮箱文件", "",
            "文本文件 (*.txt);;CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*)"
        )

        if file_path:
            self.batch_file_path_label.setText(file_path.split('/')[-1])
            self.batch_file_path = file_path
            self.add_activity("📁", f"选择批量文件: {file_path.split('/')[-1]}", "刚刚")

    def start_batch_invite(self):
        """开始批量邀请"""
        if not hasattr(self, 'batch_file_path'):
            self.show_message("错误", "请先选择邮箱文件", "error")
            return

        try:
            # 读取文件
            emails = self.read_email_file(self.batch_file_path)
            if not emails:
                self.show_message("错误", "文件中没有找到有效的邮箱地址", "error")
                return

            # 显示进度条
            self.batch_progress.setVisible(True)
            self.batch_progress.setValue(0)
            self.batch_progress.setMaximum(len(emails))

            # 开始批量邀请
            self.batch_invite_emails = emails
            self.batch_invite_index = 0
            self.batch_invite_timer = QTimer()
            self.batch_invite_timer.timeout.connect(self.process_batch_invite)

            interval = self.invite_interval.value() * 1000  # 转换为毫秒
            self.batch_invite_timer.start(interval)

            self.add_activity("📧", f"开始批量邀请 {len(emails)} 个成员", "刚刚")

        except Exception as e:
            self.show_message("错误", f"批量邀请启动失败: {str(e)}", "error")

    def read_email_file(self, file_path: str) -> List[str]:
        """读取邮箱文件"""
        emails = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析邮箱
            for line in content.split('\n'):
                line = line.strip()
                if line and '@' in line:
                    # 处理CSV格式
                    if ',' in line:
                        for email in line.split(','):
                            email = email.strip()
                            if self.validate_email(email):
                                emails.append(email)
                    else:
                        if self.validate_email(line):
                            emails.append(line)

            return list(set(emails))  # 去重

        except Exception as e:
            self.show_message("错误", f"文件读取失败: {str(e)}", "error")
            return []

    def process_batch_invite(self):
        """处理批量邀请"""
        if self.batch_invite_index >= len(self.batch_invite_emails):
            # 完成批量邀请
            self.batch_invite_timer.stop()
            self.batch_progress.setVisible(False)

            self.show_message("成功", f"批量邀请完成！共处理 {len(self.batch_invite_emails)} 个邮箱", "success")
            self.add_activity("✅", f"批量邀请完成: {len(self.batch_invite_emails)} 个成员", "刚刚")
            return

        # 处理当前批次
        batch_size = self.batch_size.value()
        end_index = min(self.batch_invite_index + batch_size, len(self.batch_invite_emails))

        current_batch = self.batch_invite_emails[self.batch_invite_index:end_index]

        # 模拟发送邀请
        for email in current_batch:
            # 这里应该调用实际的API
            pass

        # 更新进度
        self.batch_invite_index = end_index
        self.batch_progress.setValue(self.batch_invite_index)

        # 添加到邀请历史
        self.update_invite_history(current_batch)

    def batch_set_plan(self, plan: str):
        """批量设置计划"""
        if not hasattr(self, 'members_table'):
            self.show_message("提示", "请先进入团队管理页面", "warning")
            return

        reply = QMessageBox.question(
            self, "确认操作",
            f"确定要将所有成员设置为{plan}吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            count = 0
            for row in range(self.members_table.rowCount()):
                plan_item = QTableWidgetItem(plan)
                if plan == "最大计划":
                    plan_item.setForeground(QColor("#f59e0b"))
                else:
                    plan_item.setForeground(QColor("#6b7280"))
                self.members_table.setItem(row, 4, plan_item)
                count += 1

            self.show_message("成功", f"成功为 {count} 个成员设置为{plan}", "success")
            self.add_activity("⭐", f"批量设置 {count} 个成员为{plan}", "刚刚")

    def delete_pending_invites(self):
        """删除待处理邀请"""
        if not hasattr(self, 'members_table'):
            self.show_message("提示", "请先进入团队管理页面", "warning")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除所有待处理的邀请吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            count = 0
            for row in range(self.members_table.rowCount() - 1, -1, -1):
                status_item = self.members_table.item(row, 2)
                if status_item and status_item.text() == "待处理":
                    self.members_table.removeRow(row)
                    count += 1

            self.show_message("成功", f"成功删除 {count} 个待处理邀请", "success")
            self.add_activity("🗑️", f"删除 {count} 个待处理邀请", "刚刚")
            self.update_team_stats()

    def delete_failed_invites(self):
        """删除失败邀请"""
        # 这里可以添加删除失败邀请的逻辑
        self.show_message("提示", "删除失败邀请功能正在开发中", "info")

    def export_members(self, export_type: str):
        """导出成员数据"""
        if not hasattr(self, 'members_table'):
            self.show_message("提示", "请先进入团队管理页面", "warning")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, f"导出{export_type}成员数据", f"{export_type}_members.csv",
                "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
            )

            if file_path:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # 写入表头
                    headers = ["邮箱", "状态", "加入时间", "计划"]
                    writer.writerow(headers)

                    # 写入数据
                    count = 0
                    for row in range(self.members_table.rowCount()):
                        status_item = self.members_table.item(row, 2)
                        if status_item:
                            status = status_item.text()

                            # 根据导出类型筛选
                            should_export = False
                            if export_type == "all":
                                should_export = True
                            elif export_type == "pending" and status in ["待处理", "邀请中"]:
                                should_export = True
                            elif export_type == "active" and status == "已加入":
                                should_export = True

                            if should_export:
                                row_data = []
                                for col in range(1, 5):  # 跳过选择列
                                    item = self.members_table.item(row, col)
                                    row_data.append(item.text() if item else "")
                                writer.writerow(row_data)
                                count += 1

                self.show_message("成功", f"成功导出 {count} 条{export_type}成员数据到: {file_path}", "success")
                self.add_activity("📊", f"导出 {count} 条{export_type}成员数据", "刚刚")

        except Exception as e:
            self.show_message("错误", f"导出失败: {str(e)}", "error")

    def create_data_page(self) -> QWidget:
        """创建数据分析页面"""
        return self.create_simple_page("📈 数据分析", "数据分析功能正在开发中...")

    def create_settings_page(self) -> QWidget:
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel("⚙️ 系统设置")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 设置卡片区域
        settings_layout = QHBoxLayout()
        settings_layout.setSpacing(25)

        # API设置卡片
        api_card = self.create_api_settings_card()
        settings_layout.addWidget(api_card)

        # 界面设置卡片
        ui_card = self.create_ui_settings_card()
        settings_layout.addWidget(ui_card)

        layout.addLayout(settings_layout)

        # 高级设置卡片
        advanced_card = self.create_advanced_settings_card()
        layout.addWidget(advanced_card)

        layout.addStretch()
        return page

    def create_api_settings_card(self) -> QFrame:
        """创建API设置卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🔗 API设置")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # Cookie设置
        cookie_label = QLabel("Cookie:")
        cookie_label.setStyleSheet("color: #374151; font-weight: 500;")
        layout.addWidget(cookie_label)

        self.cookie_input = QTextEdit()
        self.cookie_input.setFixedHeight(100)
        self.cookie_input.setPlaceholderText("请输入Cookie信息...")
        self.cookie_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                background: #ffffff;
                font-family: monospace;
            }
            QTextEdit:focus {
                border-color: #2563eb;
            }
        """)
        layout.addWidget(self.cookie_input)

        # API超时设置
        timeout_layout = QHBoxLayout()
        timeout_label = QLabel("API超时(秒):")
        timeout_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.api_timeout = QSpinBox()
        self.api_timeout.setRange(5, 120)
        self.api_timeout.setValue(30)
        self.api_timeout.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px;
                background: #ffffff;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #2563eb;
            }
        """)

        timeout_layout.addWidget(timeout_label)
        timeout_layout.addWidget(self.api_timeout)
        timeout_layout.addStretch()

        layout.addLayout(timeout_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        test_btn = QPushButton("🔍 测试连接")
        test_btn.setStyleSheet(self.get_button_style("info"))
        test_btn.clicked.connect(self.test_api_connection)

        save_api_btn = QPushButton("💾 保存API设置")
        save_api_btn.setStyleSheet(self.get_button_style("primary"))
        save_api_btn.clicked.connect(self.save_api_settings)

        button_layout.addWidget(test_btn)
        button_layout.addWidget(save_api_btn)

        layout.addLayout(button_layout)

        return card

    def create_ui_settings_card(self) -> QFrame:
        """创建界面设置卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🎨 界面设置")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 主题设置
        theme_layout = QHBoxLayout()
        theme_label = QLabel("主题:")
        theme_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.theme_light_btn = QPushButton("☀️ 浅色")
        self.theme_light_btn.setStyleSheet(self.get_button_style("secondary"))
        self.theme_light_btn.clicked.connect(lambda: self.change_theme("light"))

        self.theme_dark_btn = QPushButton("🌙 深色")
        self.theme_dark_btn.setStyleSheet(self.get_button_style("secondary"))
        self.theme_dark_btn.clicked.connect(lambda: self.change_theme("dark"))

        theme_layout.addWidget(theme_label)
        theme_layout.addWidget(self.theme_light_btn)
        theme_layout.addWidget(self.theme_dark_btn)
        theme_layout.addStretch()

        layout.addLayout(theme_layout)

        # 自动刷新设置
        refresh_layout = QHBoxLayout()
        refresh_label = QLabel("自动刷新:")
        refresh_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.auto_refresh_check = QCheckBox("启用自动刷新")
        self.auto_refresh_check.setChecked(True)
        self.auto_refresh_check.setStyleSheet("color: #374151;")

        refresh_layout.addWidget(refresh_label)
        refresh_layout.addWidget(self.auto_refresh_check)
        refresh_layout.addStretch()

        layout.addLayout(refresh_layout)

        # 刷新间隔设置
        interval_layout = QHBoxLayout()
        interval_label = QLabel("刷新间隔(秒):")
        interval_label.setStyleSheet("color: #374151; font-weight: 500;")

        self.refresh_interval = QSpinBox()
        self.refresh_interval.setRange(10, 300)
        self.refresh_interval.setValue(30)
        self.refresh_interval.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px;
                background: #ffffff;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #2563eb;
            }
        """)

        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.refresh_interval)
        interval_layout.addStretch()

        layout.addLayout(interval_layout)

        # 保存按钮
        save_ui_btn = QPushButton("💾 保存界面设置")
        save_ui_btn.setStyleSheet(self.get_button_style("primary"))
        save_ui_btn.clicked.connect(self.save_ui_settings)

        layout.addWidget(save_ui_btn)

        return card

    def create_advanced_settings_card(self) -> QFrame:
        """创建高级设置卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🔧 高级设置")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 设置选项
        options_layout = QHBoxLayout()
        options_layout.setSpacing(20)

        # 调试模式
        debug_check = QCheckBox("启用调试模式")
        debug_check.setStyleSheet("color: #374151;")

        # 自动保存
        autosave_check = QCheckBox("启用自动保存")
        autosave_check.setChecked(True)
        autosave_check.setStyleSheet("color: #374151;")

        # 错误报告
        error_report_check = QCheckBox("发送错误报告")
        error_report_check.setStyleSheet("color: #374151;")

        options_layout.addWidget(debug_check)
        options_layout.addWidget(autosave_check)
        options_layout.addWidget(error_report_check)
        options_layout.addStretch()

        layout.addLayout(options_layout)

        # 数据管理按钮
        data_layout = QHBoxLayout()
        data_layout.setSpacing(15)

        clear_cache_btn = QPushButton("🗑️ 清除缓存")
        clear_cache_btn.setStyleSheet(self.get_button_style("warning"))
        clear_cache_btn.clicked.connect(self.clear_cache)

        reset_settings_btn = QPushButton("🔄 重置设置")
        reset_settings_btn.setStyleSheet(self.get_button_style("danger"))
        reset_settings_btn.clicked.connect(self.reset_settings)

        export_settings_btn = QPushButton("📤 导出设置")
        export_settings_btn.setStyleSheet(self.get_button_style("info"))
        export_settings_btn.clicked.connect(self.export_settings)

        import_settings_btn = QPushButton("📥 导入设置")
        import_settings_btn.setStyleSheet(self.get_button_style("success"))
        import_settings_btn.clicked.connect(self.import_settings)

        data_layout.addWidget(clear_cache_btn)
        data_layout.addWidget(reset_settings_btn)
        data_layout.addWidget(export_settings_btn)
        data_layout.addWidget(import_settings_btn)

        layout.addLayout(data_layout)

        return card

    # 设置页面功能方法
    def test_api_connection(self):
        """测试API连接"""
        cookie = self.cookie_input.toPlainText().strip()
        if not cookie:
            self.show_message("错误", "请先输入Cookie信息", "error")
            return

        # 模拟API测试
        self.show_message("测试中", "正在测试API连接...", "info")

        # 使用定时器模拟异步测试
        test_timer = QTimer()
        test_timer.timeout.connect(lambda: self.api_test_result(True))
        test_timer.setSingleShot(True)
        test_timer.start(2000)

        self.add_activity("🔍", "测试API连接", "刚刚")

    def api_test_result(self, success: bool):
        """API测试结果"""
        if success:
            self.show_message("成功", "API连接测试成功！", "success")
            self.add_activity("✅", "API连接测试成功", "刚刚")
        else:
            self.show_message("失败", "API连接测试失败，请检查Cookie设置", "error")
            self.add_activity("❌", "API连接测试失败", "刚刚")

    def save_api_settings(self):
        """保存API设置"""
        cookie = self.cookie_input.toPlainText().strip()
        timeout = self.api_timeout.value()

        if not cookie:
            self.show_message("错误", "Cookie不能为空", "error")
            return

        # 这里应该保存到配置文件
        self.show_message("成功", "API设置已保存", "success")
        self.add_activity("💾", "保存API设置", "刚刚")

    def change_theme(self, theme: str):
        """切换主题"""
        self.show_message("提示", f"主题切换功能正在开发中\n选择的主题: {theme}", "info")
        self.add_activity("🎨", f"切换到{theme}主题", "刚刚")

    def save_ui_settings(self):
        """保存界面设置"""
        auto_refresh = self.auto_refresh_check.isChecked()
        interval = self.refresh_interval.value()

        # 这里应该保存到配置文件
        self.show_message("成功", "界面设置已保存", "success")
        self.add_activity("💾", "保存界面设置", "刚刚")

    def clear_cache(self):
        """清除缓存"""
        reply = QMessageBox.question(
            self, "确认清除",
            "确定要清除所有缓存数据吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.show_message("成功", "缓存已清除", "success")
            self.add_activity("🗑️", "清除缓存", "刚刚")

    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置到默认值吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 重置所有设置
            self.cookie_input.clear()
            self.api_timeout.setValue(30)
            self.auto_refresh_check.setChecked(True)
            self.refresh_interval.setValue(30)

            self.show_message("成功", "设置已重置到默认值", "success")
            self.add_activity("🔄", "重置所有设置", "刚刚")

    def export_settings(self):
        """导出设置"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出设置", "settings.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                import json
                settings = {
                    "api": {
                        "cookie": self.cookie_input.toPlainText(),
                        "timeout": self.api_timeout.value()
                    },
                    "ui": {
                        "auto_refresh": self.auto_refresh_check.isChecked(),
                        "refresh_interval": self.refresh_interval.value()
                    }
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)

                self.show_message("成功", f"设置已导出到: {file_path}", "success")
                self.add_activity("📤", "导出设置", "刚刚")

        except Exception as e:
            self.show_message("错误", f"导出失败: {str(e)}", "error")

    def import_settings(self):
        """导入设置"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入设置", "",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 应用设置
                if "api" in settings:
                    api_settings = settings["api"]
                    if "cookie" in api_settings:
                        self.cookie_input.setPlainText(api_settings["cookie"])
                    if "timeout" in api_settings:
                        self.api_timeout.setValue(api_settings["timeout"])

                if "ui" in settings:
                    ui_settings = settings["ui"]
                    if "auto_refresh" in ui_settings:
                        self.auto_refresh_check.setChecked(ui_settings["auto_refresh"])
                    if "refresh_interval" in ui_settings:
                        self.refresh_interval.setValue(ui_settings["refresh_interval"])

                self.show_message("成功", f"设置已从 {file_path} 导入", "success")
                self.add_activity("📥", "导入设置", "刚刚")

        except Exception as e:
            self.show_message("错误", f"导入失败: {str(e)}", "error")

    def create_simple_page(self, title: str, description: str) -> QWidget:
        """创建简单页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)

        # 页面描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 20px;
        """)
        layout.addWidget(desc_label)

        # 内容卡片
        content_card = QFrame()
        content_card.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        card_layout = QVBoxLayout(content_card)
        card_layout.setContentsMargins(20, 20, 20, 20)

        # 卡片内容
        content_label = QLabel("功能正在开发中...")
        content_label.setStyleSheet("""
            font-size: 16px;
            color: #374151;
            text-align: center;
            padding: 40px;
        """)
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        card_layout.addWidget(content_label)
        layout.addWidget(content_card)
        layout.addStretch()

        return page


class SimpleModernUI(QMainWindow):
    """简化版现代化UI"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("团队管理工具 - 现代化界面")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 初始化UI
        self.init_ui()
        self.apply_styles()

    def set_api_client(self, api_client):
        """设置API客户端"""
        if hasattr(self, 'content_area'):
            self.content_area.set_api_client(api_client)
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = SimpleSidebar()
        self.sidebar.page_changed.connect(self.switch_page)
        
        # 创建内容区域
        self.content_area = SimpleContentArea()
        
        # 添加到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.content_area, 1)
        
        # 设置默认页面
        self.content_area.switch_to_page("dashboard")
    
    def switch_page(self, page_id: str):
        """切换页面"""
        self.content_area.switch_to_page(page_id)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: #f9fafb;
            }
        """)
